# Vehicle API

A FastAPI-based service for vehicle data. The API provides endpoints for vehicle records, valuations, and related data, with robust validation and mapping logic.

## Features
- RESTful API for vehicle and valuation data
- Pydantic models with strict type and field validation
- Supabase integration for data storage and retrieval
- Custom mapping service to ensure required fields are present
- Test coverage for all core logic
- Extensible for new data sources and models

## Setup

### Prerequisites
- Python 3.10+
- [Poetry](https://python-poetry.org/) or `pip`
- Supabase credentials (if using Supabase backend)

### Installation
```bash
# Clone the repository
$ git clone <repo-url>
$ cd carma-python/src/apis/vehicle-api

# Install dependencies
$ poetry install
# or
$ pip install -r requirements.txt
```

### Environment Variables
- `SUPABASE_URL`: Supabase project URL
- `SUPABASE_KEY`: Supabase service key
- `API_TOKEN`: Token for authentication (if enabled)

Create a `.env` file or export these variables in your shell.

### Running the API
```bash
$ uvicorn app:app --reload
```
The API will be available at `http://localhost:8000`.

### API Documentation
Interactive docs are available at:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### Testing
```bash
$ pytest service/src/tests/unit
```

## Model & Data Mapping
- Vehicle and Valuation models are defined in `service/src/models.py`.
- Field types are mapped directly from the latest database schema (see CSVs in `/Users/<USER>/Downloads/`).
- Required fields are enforced using Pydantic and custom mapping logic.
- The mapping service ensures all fields with the `bc_required` attribute are present and non-empty.

## Project Structure
- `app.py` - FastAPI app factory
- `models.py` - Pydantic models for all data
- `services/` - Business logic, mapping, and Supabase integration
- `tests/unit/` - Unit tests for all major components

## Contributing
Pull requests and issues are welcome. Please ensure all tests pass and code is formatted with `black` and `isort`.

## License
MIT
