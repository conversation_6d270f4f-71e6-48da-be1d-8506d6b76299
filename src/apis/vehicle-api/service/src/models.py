from typing import Any, Dict, Generic, List, Optional, TypeVar, Union

from pydantic import BaseModel, Field, field_validator


class Payload(BaseModel):
    skus: list[str]


class ResponsePayload(BaseModel):
    skus: list[str]


class Vehicle(BaseModel):
    # The following export fields are missing mappings in the Vehicle model:
    #   - Buyer Code # not required.
    #   - Inspector Code # not required
    #   - Build Date
    #   - Build Month
    #   - Variant
    #   - Engine No.
    #   - Vehicle Source
    #   - Purchase Location
    #   - Current Location
    #   - CRM Purchased Price Inc(GST)
    #   - Motor Dealer Licence No.
    #   - Item Category Code
    #   - Gen. Prod. Posting Group
    #   - Inventory Posting Group
    #   - InboundStatus
    #   - Vendor No.

    id: int = Field(
        ..., json_schema_extra={"custom": "primary key", "bc_required": "id"}
    )
    acquisition_id: str = Field(
        ...,
        json_schema_extra={"custom": "external id", "bc_required": "acquisition_id"},
    )
    acquisition_version: int = Field(...)
    acquisition_version_created_by: str = Field(...)
    acquisition_version_created_at: str = Field(...)
    acquisition_created_at: str = Field(...)
    rego: Optional[str] = Field(None, json_schema_extra={"bc_required": "rego"})
    state: Optional[str] = Field(None, json_schema_extra={"bc_required": "state"})
    keys: Optional[str] = Field(None, json_schema_extra={"bc_required": "keys"})
    vin: Optional[str] = Field(None, json_schema_extra={"bc_required": "vin"})
    nvic: Optional[str] = Field(None)
    year: Optional[int] = Field(None, json_schema_extra={"bc_required": "year"})
    make: Optional[str] = Field(None, json_schema_extra={"bc_required": "make"})
    model: Optional[str] = Field(None, json_schema_extra={"bc_required": "model"})
    series: Optional[str] = Field(None)
    badge: Optional[str] = Field(None)
    transmission: Optional[str] = Field(
        None, json_schema_extra={"bc_required": "transmission"}
    )
    odometer: Optional[int] = Field(None, json_schema_extra={"bc_required": "odometer"})
    missing_service_history: Optional[str] = Field(None)
    fuel_type: Optional[str] = Field(None)
    genuine_service_history: Optional[str] = Field(None)
    warranty_status: Optional[str] = Field(None)
    vehicle_segment: Optional[str] = Field(None)
    exterior_condition_image_urls: Optional[list[str]] = Field(None)
    interior_condition_image_urls: Optional[list[str]] = Field(None)
    car_location: Optional[str] = Field(None)
    vehicle_description: Optional[str] = Field(None)
    customer_name: Optional[str] = Field(None)
    customer_phone: Optional[str] = Field(None)
    customer_email: Optional[str] = Field(None)
    car_location_lat: Optional[float] = Field(None)
    car_location_lng: Optional[float] = Field(None)
    distribution_channel: Optional[str] = Field(None)
    acquisition_channel: Optional[str] = Field(None)
    mechanical_damage: Optional[bool] = Field(None)
    dash_warning_lights: Optional[list[str]] = Field(None)
    debug: Optional[bool] = Field(False)
    vehicle_priority: Optional[str] = Field(None)
    first_owner: Optional[bool] = Field(None)
    web_tradein_id: Optional[str] = Field(None)
    options_or_extras: Optional[str] = Field(None)
    customer_expectation: Optional[int] = Field(None)
    major_accident: Optional[bool] = Field(None)
    distance_driven_on_current_tyres: Optional[int] = Field(None)
    finance_owing: Optional[bool] = Field(None)
    timeline_to_sell: Optional[str] = Field(None)
    has_exterior_damage: Optional[bool] = Field(None)
    has_interior_damage: Optional[bool] = Field(None)
    number_of_tyres_to_change: Optional[str] = Field(None)
    modifications_or_aftermarket_accessories: Optional[list[str]] = Field(None)
    has_services_done_by_manufacturer: Optional[bool] = Field(None)
    has_factory_installed_options: Optional[bool] = Field(None)
    applicable_installed_options: Optional[list[str]] = Field(None)
    other_optional_extras: Optional[list[str]] = Field(None)
    has_service_records_specified_by_manufacturer: Optional[bool] = Field(None)
    acquisition_status: Optional[str] = Field("as_new")
    representative: Optional[int] = Field(None)
    vehicle_notes: Optional[str] = Field(None)
    unv_acquisiton_id: Optional[str] = Field(None)
    number_of_major_damage_panels: Optional[str] = Field(None)
    number_of_minor_damage_panels: Optional[str] = Field(None)
    number_of_rips_or_tears: Optional[str] = Field(None)
    number_of_wheel_damage: Optional[int] = Field(None)
    number_of_stains: Optional[str] = Field(None)
    has_interior_damage_to_digital_screens: Optional[bool] = Field(None)
    has_interior_damage_to_panel_or_dashboard: Optional[bool] = Field(None)
    customer_notes: Optional[str] = Field(None)
    has_hail_damage: Optional[bool] = Field(None)
    has_persistent_odours: Optional[bool] = Field(None)
    acquisition_source: Optional[str] = Field(None)
    acquisition_source_additional_data: Optional[dict] = Field(None)
    customer_buying_intent: Optional[bool] = Field(None)
    has_windscreen_chips: Optional[bool] = Field(None)
    has_windscreen_cracks: Optional[bool] = Field(None)
    seller_ad_url: Optional[str] = Field(None)
    seller_autograb_url: Optional[str] = Field(None)
    body_type: Optional[str] = Field(None)
    photos: Optional[list[dict]] = Field(None)
    vehicle_data_found: Optional[bool] = Field(None)
    vehicle_data_correct: Optional[bool] = Field(None)
    missing_anything_notes: Optional[str] = Field(None)
    warranty_months: Optional[int] = Field(None)
    warranty_kms: Optional[int] = Field(None)
    customer_first_name: Optional[str] = Field(None)
    customer_last_name: Optional[str] = Field(None)
    car_location_suburb: Optional[str] = Field(None)
    car_location_postcode: Optional[int] = Field(None)
    car_location_state: Optional[str] = Field(None)
    seller_advertised_price: Optional[int] = Field(None)
    hubspot_data: Optional[dict] = Field(None)
    hubspot_pipeline_id: Optional[str] = Field(None)
    hubspot_stage_id: Optional[str] = Field(None)
    build_year: Optional[int] = Field(None)
    rego_state: Optional[str] = Field(None)
    model_year: Optional[int] = Field(None)
    hubspot_acquisition_id: Optional[str] = Field(None)
    drive_type: Optional[str] = Field(None)
    engine_size: Optional[int] = Field(None)
    value_guide: Optional[str] = Field(None)
    compliance_date: Optional[str] = Field(None)
    accident_overview: Optional[str] = Field(None)
    mechanical_issues_overview: Optional[str] = Field(None)
    more_info_required: Optional[bool] = Field(None)
    more_info_required_comments: Optional[str] = Field(None)
    num_seats: Optional[int] = Field(None)
    body_config_type: Optional[str] = Field(None)
    rego_expiry: Optional[str] = Field(None)
    


class VehicleWithBCStatus(Vehicle):
    """Vehicle model with BC validation status."""

    ready_for_bc: bool


T = TypeVar("T")


class MetaData(BaseModel):
    # Pagination and validation metadata
    page: Optional[int] = None
    size: Optional[int] = None
    total_records: Optional[int] = None
    total_pages: Optional[int] = None
    validate_bc: Optional[bool] = None
    ready_for_bc: Optional[bool] = None
    is_valid: Optional[bool] = None

    @field_validator("total_pages", mode="before")
    @classmethod
    def calc_pages(cls, v, info):
        page = info.data.get("page")
        size = info.data.get("size")
        total = info.data.get("total_records")
        if v is None and page is not None and size is not None and total is not None:
            return (total + size - 1) // size
        return v


class Envelope(BaseModel, Generic[T]):
    data: T
    meta: Optional[MetaData] = None
    status: str = "success"
    message: Optional[str] = None


class VehicleValuation(BaseModel):
    id: int = Field(...)
    valuation_version_created_by: str = Field(...)
    acquisition_version_id: int = Field(...)
    market_leader_price_egc: Optional[int] = Field(None)
    rmadj_market_signals: Optional[int] = Field(None)
    mlpadj_warranty_service_history: Optional[int] = Field(None)
    mlpadj_quality_factors: Optional[int] = Field(None)
    mlpadj_attribute_factors_up: Optional[int] = Field(None)
    mlpadj_attribute_factors_down: Optional[int] = Field(None)
    condcost_mechanical_incgst: Optional[int] = Field(None)
    condcost_dent_incgst: Optional[int] = Field(None)
    condcost_paint_incgst: Optional[int] = Field(None)
    condcost_glass_incgst: Optional[int] = Field(None)
    condcost_rim_incgst: Optional[int] = Field(None)
    condcost_tyre_incgst: Optional[int] = Field(None)
    condcost_upholstery_incgst: Optional[int] = Field(None)
    condcost_key_incgst: Optional[int] = Field(None)
    condcost_detail_incgst: Optional[int] = Field(None)
    othercost_inspection_qc_incgst: Optional[int] = Field(None)
    othercost_labour_incgst: Optional[int] = Field(None)
    othercost_compliance_incgst: Optional[int] = Field(None)
    othercost_registration_incgst: Optional[int] = Field(None)
    othercost_transport_local_incgst: Optional[int] = Field(None)
    othercost_transport_regional_incgst: Optional[int] = Field(None)
    othercost_transport_interstate_incgst: Optional[int] = Field(None)
    othercost_miscellaneous_incgst: Optional[int] = Field(None)
    vehicle_required_channel_margin_incgst: Optional[int] = Field(None)
    rmadj_desirability_risk_incgst: Optional[int] = Field(None)
    rmadj_time_to_sell_incgst: Optional[int] = Field(None)
    overrides: Optional[dict] = Field(None)
    market_leader_price_egc_market_adj: Optional[int] = Field(None)
    days_supply_60: Optional[int] = Field(None)
    mlpadj_oversupply: Optional[int] = Field(None)
    rmadj_warranty_service_history_incgst: Optional[int] = Field(None)
    time_outbound: Optional[int] = Field(None)
    time_through_production: Optional[int] = Field(None)
    acquisition_id: Optional[str] = Field(None)
    valuation_version: int = Field(...)
    valuation_version_created_at: str = Field(...)
    valuation_id: str = Field(...)
    valuation_created_at: Optional[str] = Field(None)
    valuation_created_by: Optional[str] = Field(None)
    valuation_comments: Optional[dict] = Field(None)
    valuation_status: Optional[str] = Field("vs_ready")
    debug: Optional[bool] = Field(False)
    autograb_url_comparable: Optional[str] = Field(None)
    market_leader_price_egc_automatic: Optional[int] = Field(None)
    market_leader_price_egc_adjusted: Optional[int] = Field(None)
    market_leader_price_egc_adjusted_exgst: Optional[int] = Field(None)
    condition_cost_total_incgst: Optional[int] = Field(None)
    other_cost_total_incgst: Optional[int] = Field(None)
    vehicle_required_margin_incgst: Optional[int] = Field(None)
    offer_price_max_incgst: Optional[int] = Field(None)
    acquisition_channel: Optional[str] = Field(None)
    distribution_channel: Optional[str] = Field(None)
    market_signals_created_at: Optional[str] = Field(None)
    market_leader_price_egc_draft: Optional[int] = Field(None)
    valuation_notes: Optional[str] = Field(None)
    offer_price_incgst: Optional[int] = Field(None)
    list_price_incgst: Optional[int] = Field(None)
    valuation_type: Optional[str] = Field(None)
    valuation_completed_by: Optional[int] = Field(None)
    valuation_completed_at: Optional[str] = Field(None)
    predicted_price: Optional[int] = Field(None)
    revaluation_notes: Optional[str] = Field(None)
    ref_valuation_id: Optional[str] = Field(None)
    ref_valuation_version_id: Optional[int] = Field(None)
    ref_valuation_version: Optional[int] = Field(None)
    classification_tags: Optional[dict] = Field(None)
    mlpadj_total_incgst: Optional[int] = Field(None)
    valuation_substatus_price: Optional[str] = Field(None)
    valuation_substatus_cost: Optional[str] = Field(None)
    valuation_substatus_margin: Optional[str] = Field(None)


class VehiclePayment(BaseModel):
    # Payment fields
    id: int = Field(...)
    acquisition_id: str = Field(...)
    acquisition_version_id: Optional[int] = Field(None)
    onboarding_id: Optional[str] = Field(None)
    onboarding_version_id: Optional[int] = Field(None)
    payment_id: Optional[str] = Field(None)
    payment_status: Optional[str] = Field(None)
    payment_created_by: Optional[str] = Field(None)
    payment_created_at: Optional[str] = Field(None)
    payment_version: Optional[int] = Field(None)
    payment_version_created_by: Optional[str] = Field(None)
    payment_version_created_at: Optional[str] = Field(None)
    payment_authorised_by: Optional[str] = Field(None)
    payment_authorised_at: Optional[str] = Field(None)
    payment_completed_by: Optional[str] = Field(None)
    payment_completed_at: Optional[str] = Field(None)
    payment_amount: Optional[float] = Field(None)
    receipt_amount: Optional[float] = Field(None)
    payment_account_name: Optional[str] = Field(None)
    payment_account_bsb: Optional[str] = Field(None)
    payment_account_number: Optional[str] = Field(None)
    payment_notes: Optional[str] = Field(None)
    debug: Optional[bool] = Field(False)
    payment_type: Optional[str] = Field(None)
    payment_reference: Optional[str] = Field(None)
    nb_payments: Optional[int] = Field(None)
    payment_number: Optional[int] = Field(None)

    # Vehicle fields from last_acquisitions join
    acquisition_status: Optional[str] = Field(None)
    rego: Optional[str] = Field(None)
    build_year: Optional[int] = Field(None)
    make: Optional[str] = Field(None)
    model: Optional[str] = Field(None)
    badge: Optional[str] = Field(None)
    series: Optional[str] = Field(None)
    vehicle_short_description_1: Optional[str] = Field(None)
    sku: Optional[str] = Field(None)


class OnboardingRequirement(BaseModel):
    acquisition_id: str = Field(...)
    requirement: str = Field(...)
    requirement_status: str = Field(...)
    requirement_values: dict = Field(...)

class Onboarding(BaseModel):
    acquisition_id: str
    acquisition_version_id: Optional[int] = None
    collected_at: Optional[str] = None
    current_location_id: Optional[int] = None
    current_location_notes: Optional[str] = None
    debug: Optional[bool] = None
    finance_owing: Optional[bool] = None
    id: int
    inspector: Optional[str] = None
    key_location_description: Optional[str] = None
    onboarding_authorised_at: Optional[str] = None
    onboarding_authorised_by: Optional[str] = None
    onboarding_completed_at: Optional[str] = None
    onboarding_completed_by: Optional[str] = None
    onboarding_created_at: Optional[str] = None
    onboarding_created_by: Optional[str] = None
    onboarding_id: Optional[str] = None
    onboarding_notes: Optional[str] = None
    onboarding_status: Optional[str] = None
    onboarding_version: Optional[int] = None
    onboarding_version_created_at: Optional[str] = None
    onboarding_version_created_by: Optional[str] = None
    purchase_date: Optional[str] = None
    purchase_price: Optional[float] = None
    representative: Optional[str] = None
    requirement_status: Optional[str] = None
    seller_type: Optional[str] = None
    send_review_request: Optional[bool] = None
    sku: Optional[str] = None
    tradein_contract_setup: Optional[str] = None
    tradein_sales_order_id: Optional[str] = None
    vehicle_story: Optional[str] = None

class Inspection(BaseModel):
    id: int
    inspection_created_at: Optional[str] = None
    acquisition_version_id: Optional[int] = None
    inspection_planned_date: Optional[str] = None
    inspection_type: Optional[str] = None
    inspection_held_date: Optional[str] = None
    inspection_status: Optional[str] = None
    inspection_version_created_by: Optional[str] = None
    acquisition_id: Optional[str] = None
    inspection_version: Optional[int] = None
    inspection_version_created_at: Optional[str] = None
    inspection_id: Optional[str] = None
    inspection_created_by: Optional[str] = None
    debug: Optional[bool] = None
    inspector: Optional[int] = None
    inspection_booked_datetime: Optional[str] = None
    inspection_booked_duration: Optional[int] = None
    inspection_address: Optional[str] = None
    inspection_location_street: Optional[str] = None
    inspection_location_postcode: Optional[str] = None
    valuation_version_id: Optional[int] = None
    valuation_acquisition_version_id: Optional[int] = None
    inspection_completed_at: Optional[str] = None
    wholesale_review_tags: Optional[str] = None
    wholesale_review_notes: Optional[str] = None
    valuation_id: Optional[str] = None
    inspection_completed_by: Optional[str] = None