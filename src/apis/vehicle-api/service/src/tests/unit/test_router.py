from unittest.mock import MagicMock

import pytest
from carma.common import ExtError, ExtOk
from conftest import INCOMPLETE_VEHICLE_DATA, VALID_VEHICLE_DATA
from container import Container
from fastapi import APIRouter, Depends, FastAPI, HTTPException
from fastapi.testclient import TestClient
from models import Envelope, MetaData, Vehicle
from services.mapping_service import VehicleMappingService


def test_healthz_endpoint(client: TestClient, api_version: str):
    res = client.get(
        f"/api/{api_version}/healthz",
    )

    assert res.status_code == 200


def test_readiness_endpoint_success(client: TestClient, api_version: str):
    res = client.get(
        f"/api/{api_version}/ready",
    )

    assert res.status_code == 200


# Fixtures for GET /vehicles/{id}
@pytest.fixture
def supabase_valid():
    supabase = MagicMock()
    supabase.get_vehicle.return_value = ExtOk(VALID_VEHICLE_DATA)
    return supabase


@pytest.fixture
def supabase_invalid():
    supabase = MagicMock()
    supabase.get_vehicle.return_value = ExtOk(INCOMPLETE_VEHICLE_DATA)
    return supabase


# Add fixture for real mapping service
@pytest.fixture
def mapping_service():
    return VehicleMappingService()


def test_get_vehicle_valid(
    client: TestClient,
    api_version: str,
    token: str,
    supabase_valid,
    mapping_service,
):
    # Override the SupabaseService provider in the DI container
    container = client.app.container
    container.supabase_service.override(supabase_valid)
    # Override the VehicleMappingService dependency
    client.app.dependency_overrides[VehicleMappingService] = lambda: mapping_service

    # Call endpoint
    resp = client.get(
        f"/api/{api_version}/vehicles/{VALID_VEHICLE_DATA['id']}",
        headers={"token": token},
    )
    assert resp.status_code == 200
    body = resp.json()
    # Ensure we got the correct id and is_valid True
    assert body["data"]["id"] == VALID_VEHICLE_DATA["id"]
    assert body["data"]["acquisition_id"] == VALID_VEHICLE_DATA["acquisition_id"]
    assert body["meta"]["ready_for_bc"] is True


def test_get_vehicle_invalid(
    client: TestClient,
    api_version: str,
    token: str,
    supabase_invalid,
    mapping_service,
):
    # Override the SupabaseService provider in the DI container
    container = client.app.container
    container.supabase_service.override(supabase_invalid)
    # Override the VehicleMappingService dependency
    client.app.dependency_overrides[VehicleMappingService] = lambda: mapping_service

    # Call endpoint
    resp = client.get(
        f"/api/{api_version}/vehicles/{INCOMPLETE_VEHICLE_DATA['id']}",
        headers={"token": token},
    )
    assert resp.status_code == 200
    body = resp.json()
    # Ensure we got the correct id and is_valid False
    assert body["data"]["id"] == INCOMPLETE_VEHICLE_DATA["id"]
    assert body["meta"]["ready_for_bc"] is False


def test_list_vehicles_valid(
    client: TestClient, api_version: str, token: str, supabase_valid, mapping_service
):
    supabase_mock = MagicMock()
    supabase_mock.list_vehicles.return_value = ([VALID_VEHICLE_DATA], 1)

    container = client.app.container
    container.supabase_service.override(supabase_mock)
    client.app.dependency_overrides[VehicleMappingService] = lambda: mapping_service

    resp = client.get(f"/api/{api_version}/vehicles", headers={"token": token})
    assert resp.status_code == 200
    body = resp.json()
    assert isinstance(body["data"], list)
    assert body["meta"]["validate_bc"] is False


def test_list_vehicles_not_found(
    client: TestClient, api_version: str, token: str, mapping_service
):
    supabase_mock = MagicMock()
    supabase_mock.list_vehicles.return_value = (None, 0)

    container = client.app.container
    container.supabase_service.override(supabase_mock)
    client.app.dependency_overrides[VehicleMappingService] = lambda: mapping_service

    resp = client.get(f"/api/{api_version}/vehicles", headers={"token": token})
    assert resp.status_code == 404


def test_get_vehicle_valuations_valid(client: TestClient, api_version: str, token: str):
    supabase_mock = MagicMock()
    supabase_mock.get_vehicle_valuations.return_value = ExtOk(
        {"valuation_id": "val-1", "amount": 12345}
    )

    container = client.app.container
    container.supabase_service.override(supabase_mock)
    resp = client.get(
        f"/api/{api_version}/vehicles/1/valuations", headers={"token": token}
    )
    assert resp.status_code == 200

    body = resp.json()
    assert body["data"]["valuation_id"] == "val-1"
    assert body["meta"]["is_valid"] is True


def test_get_vehicle_valuations_not_found(
    client: TestClient, api_version: str, token: str
):
    supabase_mock = MagicMock()
    supabase_mock.get_vehicle_valuations.return_value = ExtOk({})

    container = client.app.container
    container.supabase_service.override(supabase_mock)
    resp = client.get(
        f"/api/{api_version}/vehicles/1/valuations", headers={"token": token}
    )
    assert resp.status_code == 404


def test_get_vehicle_valuations_error(client: TestClient, api_version: str, token: str):
    supabase_mock = MagicMock()
    supabase_mock.get_vehicle_valuations.return_value = ExtError(
        code=500, message="Something went wrong"
    )

    container = client.app.container
    container.supabase_service.override(supabase_mock)
    resp = client.get(
        f"/api/{api_version}/vehicles/1/valuations", headers={"token": token}
    )
    assert resp.status_code == 500


def test_patch_vehicle_valid(
    client: TestClient, api_version: str, token: str, mapping_service
):
    supabase_mock = MagicMock()
    # Patch returns updated vehicle data
    patched_data = VALID_VEHICLE_DATA.copy()
    patched_data["make"] = "Honda"
    supabase_mock.patch_vehicle.return_value = ExtOk(patched_data)

    container = client.app.container
    container.supabase_service.override(supabase_mock)
    client.app.dependency_overrides[VehicleMappingService] = lambda: mapping_service

    resp = client.patch(
        f"/api/{api_version}/vehicles/{VALID_VEHICLE_DATA['id']}",
        headers={"token": token},
        json={"make": "Honda"},
    )
    assert resp.status_code == 200
    body = resp.json()
    assert body["data"]["make"] == "Honda"
    assert body["data"]["id"] == VALID_VEHICLE_DATA["id"]


def test_patch_vehicle_excludes_id(
    client: TestClient, api_version: str, token: str, mapping_service
):
    supabase_mock = MagicMock()
    # Patch returns vehicle data with updated make (id should not change)
    patched_data = VALID_VEHICLE_DATA.copy()
    patched_data["make"] = "Mazda"
    supabase_mock.patch_vehicle.return_value = ExtOk(patched_data)

    container = client.app.container
    container.supabase_service.override(supabase_mock)
    client.app.dependency_overrides[VehicleMappingService] = lambda: mapping_service

    resp = client.patch(
        f"/api/{api_version}/vehicles/{VALID_VEHICLE_DATA['id']}",
        headers={"token": token},
        json={"id": 999, "make": "Mazda"},
    )
    assert resp.status_code == 200
    body = resp.json()
    # id should remain unchanged
    assert body["data"]["id"] == VALID_VEHICLE_DATA["id"]
    assert body["data"]["make"] == "Mazda"


def test_patch_vehicle_not_found(
    client: TestClient, api_version: str, token: str, mapping_service
):
    supabase_mock = MagicMock()
    supabase_mock.patch_vehicle.return_value = ExtOk({})

    container = client.app.container
    container.supabase_service.override(supabase_mock)
    client.app.dependency_overrides[VehicleMappingService] = lambda: mapping_service

    resp = client.patch(
        f"/api/{api_version}/vehicles/{VALID_VEHICLE_DATA['id']}",
        headers={"token": token},
        json={"make": "Ford"},
    )
    assert resp.status_code == 404


def test_patch_vehicle_error(
    client: TestClient, api_version: str, token: str, mapping_service
):
    supabase_mock = MagicMock()
    supabase_mock.patch_vehicle.return_value = ExtError(code=500, message="Patch error")

    container = client.app.container
    container.supabase_service.override(supabase_mock)
    client.app.dependency_overrides[VehicleMappingService] = lambda: mapping_service

    resp = client.patch(
        f"/api/{api_version}/vehicles/{VALID_VEHICLE_DATA['id']}",
        headers={"token": token},
        json={"make": "Ford"},
    )
    assert resp.status_code == 500


def test_get_vehicle_payments_success(client: TestClient, api_version: str, token: str):
    supabase_mock = MagicMock()
    payment_data = [
        {
            "id": 1,
            "acquisition_id": "A-1",
            "payment_id": "P-1",
            "payment_amount": 50000.0,
            "payment_status": "completed",
            "payment_created_at": "2024-01-01T00:00:00Z",
            "payment_type": "EFT",
            "rego": "ABC123",
            "make": "Toyota",
            "model": "Corolla",
            "sku": "SKU123",
        }
    ]
    supabase_mock.get_vehicle_payments.return_value = ExtOk(payment_data)

    container = client.app.container
    container.supabase_service.override(supabase_mock)

    resp = client.get(
        f"/api/{api_version}/vehicles/A-1/payments", headers={"token": token}
    )
    assert resp.status_code == 200
    body = resp.json()
    assert len(body["data"]) == 1
    assert body["data"][0]["payment_id"] == "P-1"
    assert "payment_amount" in body["data"][0]
    assert body["data"][0]["payment_status"] == "completed"


def test_get_vehicle_payments_empty(client: TestClient, api_version: str, token: str):
    supabase_mock = MagicMock()
    supabase_mock.get_vehicle_payments.return_value = ExtOk([])

    container = client.app.container
    container.supabase_service.override(supabase_mock)

    resp = client.get(
        f"/api/{api_version}/vehicles/A-999/payments", headers={"token": token}
    )
    assert resp.status_code == 200
    body = resp.json()
    assert body["data"] == []


def test_get_vehicle_payments_error(client: TestClient, api_version: str, token: str):
    supabase_mock = MagicMock()
    supabase_mock.get_vehicle_payments.return_value = ExtError(
        code=500, message="Database error"
    )

    container = client.app.container
    container.supabase_service.override(supabase_mock)

    resp = client.get(
        f"/api/{api_version}/vehicles/A-1/payments", headers={"token": token}
    )
    assert resp.status_code == 500


def test_get_vehicle_onboarding_requirements_success(
    client: TestClient, api_version: str, token: str
):
    requirement_data = [
        {
            "acquisition_id": "A-1",
            "requirement": "Documentation",
            "requirement_status": "completed",
            "requirement_values": {"doc_type": "license", "uploaded": True},
        },
        {
            "acquisition_id": "A-1",
            "requirement": "Inspection",
            "requirement_status": "pending",
            "requirement_values": {
                "inspector": "John Doe",
                "scheduled_date": "2024-01-15",
            },
        },
    ]

    supabase_mock = MagicMock()
    supabase_mock.get_vehicle_onboarding_requirements.return_value = ExtOk(
        requirement_data
    )

    container = client.app.container
    container.supabase_service.override(supabase_mock)

    resp = client.get(
        f"/api/{api_version}/vehicles/A-1/onboarding_requirements",
        headers={"token": token},
    )
    assert resp.status_code == 200

    body = resp.json()
    assert len(body["data"]) == 2
    assert body["data"][0]["requirement"] == "Documentation"
    assert body["data"][0]["requirement_values"]["doc_type"] == "license"
    assert body["data"][1]["requirement_status"] == "pending"


def test_get_vehicle_onboarding_requirements_empty(
    client: TestClient, api_version: str, token: str
):
    supabase_mock = MagicMock()
    supabase_mock.get_vehicle_onboarding_requirements.return_value = ExtOk([])

    container = client.app.container
    container.supabase_service.override(supabase_mock)

    resp = client.get(
        f"/api/{api_version}/vehicles/A-999/onboarding_requirements",
        headers={"token": token},
    )
    assert resp.status_code == 200

    body = resp.json()
    assert body["data"] == []
    assert body["meta"] is not None


def test_get_vehicle_onboarding_requirements_error(
    client: TestClient, api_version: str, token: str
):
    supabase_mock = MagicMock()
    supabase_mock.get_vehicle_onboarding_requirements.return_value = ExtError(
        code=500, message="Database error"
    )

    container = client.app.container
    container.supabase_service.override(supabase_mock)

    resp = client.get(
        f"/api/{api_version}/vehicles/A-1/onboarding_requirements",
        headers={"token": token},
    )
    assert resp.status_code == 500
