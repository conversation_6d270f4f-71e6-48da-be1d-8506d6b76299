import importlib
import os
import sys

"""
Ensure the code directory (service/src) is on the import path.
This works both when running tests from the service directory and from the monorepo root.
"""


def find_service_src_dir():
    """Find the service/src directory regardless of where tests are run from."""
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # Walk up the directory tree to find the service/src directory
    while current_dir != os.path.dirname(current_dir):  # Stop at filesystem root
        # Check if we're in a tests directory under service/src
        if current_dir.endswith("service/src/tests/unit") or current_dir.endswith(
            "service/src/tests"
        ):
            service_src = os.path.join(
                current_dir.split("service/src/tests")[0], "service/src"
            )
            if os.path.exists(service_src):
                return service_src

        # Check if current directory is service/src
        if current_dir.endswith("service/src") and os.path.exists(
            os.path.join(current_dir, "app.py")
        ):
            return current_dir

        # Check if service/src exists as a subdirectory
        potential_service_src = os.path.join(current_dir, "service", "src")
        if os.path.exists(potential_service_src) and os.path.exists(
            os.path.join(potential_service_src, "app.py")
        ):
            return potential_service_src

        current_dir = os.path.dirname(current_dir)

    # Fallback to the original calculation
    return os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))


CODE_DIR = find_service_src_dir()
if CODE_DIR not in sys.path:
    sys.path.insert(0, CODE_DIR)

# Now we can import local modules
import app as _app_mod
import pytest
import router.routes as _routes_mod
import settings as _settings_mod
from dependency_injector import providers
from fastapi.testclient import TestClient
from services.mapping_service import VehicleMappingService

importlib.reload(_settings_mod)
importlib.reload(_routes_mod)
importlib.reload(_app_mod)

# Import FastAPI app factory and auth dependency
from app import create_app, create_container
from router.utils import check_token

# Test constants
API_VERSION = "v2"
TOKEN = "token"

# Now reload relevant modules so Settings picks up test env


# Test data for vehicles
VALID_VEHICLE_DATA = {
    "id": 1,
    "acquisition_id": "A-1",
    "acquisition_version": 1,
    "acquisition_version_created_by": "test_user",
    "acquisition_version_created_at": "2024-01-01T00:00:00Z",
    "acquisition_created_at": "2024-01-01T00:00:00Z",
    "rego": "XYZ123",
    "state": "NSW",
    "keys": "2",
    "vin": "VIN0001",
    "nvic": None,
    "year": 2020,
    "make": "Toyota",
    "model": "Corolla",
    "series": None,
    "badge": None,
    "transmission": "manual",
    "odometer": 50000,
    "missing_service_history": None,
    "fuel_type": None,
    "genuine_service_history": None,
    "warranty_status": None,
    "vehicle_segment": None,
    "exterior_condition_image_urls": None,
    "interior_condition_image_urls": None,
    "car_location": None,
    "description": None,
    "customer_name": None,
    "customer_phone": None,
    "customer_email": None,
    "car_location_lat": None,
    "car_location_lng": None,
    "distribution_channel": None,
    "acquisition_channel": None,
    "mechanical_damage": None,
    "dash_warning_lights": None,
    "debug": None,
    "vehicle_priority": None,
    "first_owner": None,
    "web_tradein_id": None,
    "options_or_extras": None,
    "customer_expectation": None,
    "major_accident": None,
    "distance_driven_on_current_tyres": None,
    "finance_owing": None,
    "timeline_to_sell": None,
    "has_exterior_damage": None,
    "has_interior_damage": None,
    "number_of_tyres_to_change": None,
    "modifications_or_aftermarket_accessories": None,
    "has_services_done_by_manufacturer": None,
    "has_factory_installed_options": None,
    "applicable_installed_options": None,
    "other_optional_extras": None,
    "has_service_records_specified_by_manufacturer": None,
    "acquisition_status": None,
    "representative": None,
    "vehicle_notes": None,
    "unv_acquisiton_id": None,
    "number_of_major_damage_panels": None,
    "number_of_minor_damage_panels": None,
    "number_of_rips_or_tears": None,
    "number_of_wheel_damage": None,
    "number_of_stains": None,
    "has_interior_damage_to_digital_screens": None,
    "has_interior_damage_to_panel_or_dashboard": None,
    "customer_notes": None,
    "has_hail_damage": None,
    "has_persistent_odours": None,
    "acquisition_source": None,
    "acquisition_source_additional_data": None,
    "customer_buying_intent": None,
    "has_windscreen_chips": None,
    "has_windscreen_cracks": None,
    "seller_ad_url": None,
    "seller_autograb_url": None,
    "body_type": None,
    "photos": None,
    "vehicle_data_found": None,
    "vehicle_data_correct": None,
    "missing_anything_notes": None,
    "warranty_months": None,
    "warranty_kms": None,
    "customer_first_name": None,
    "customer_last_name": None,
    "car_location_suburb": None,
    "car_location_postcode": None,
    "car_location_state": None,
    "seller_advertised_price": None,
    "hubspot_data": None,
    "hubspot_pipeline_id": None,
    "hubspot_stage_id": None,
    "build_year": None,
    "rego_state": None,
    "model_year": None,
    "hubspot_acquisition_id": None,
    "drive_type": None,
    "engine_size": None,
    "value_guide": None,
    "compliance_date": None,
    "num_seats": None,
    "body_config_type": None,
    "hubspot_owner_id": None,
    "more_info_required": None,
    "more_info_required_comments": None,
    "sku": None,
    "is_driveable": None,
    "mechanical_issues_overview": None,
    "accident_overview": None,
    "repair_status": None,
    "is_write_off": None,
    "rego_expiry": None,
}

INCOMPLETE_VEHICLE_DATA = {
    "id": 2,
    # missing required fields
}


@pytest.fixture
def api_version():
    return API_VERSION


@pytest.fixture
def token():
    return TOKEN


@pytest.fixture
def test_app(supabase_valid, mapping_service):
    # Create DI container and override supabase_service provider
    container = create_container()
    container.supabase_service.override(providers.Factory(lambda: supabase_valid))
    # Create the FastAPI app with wired container
    app = create_app(container)
    # Override mapping service dependency
    app.dependency_overrides[VehicleMappingService] = lambda: mapping_service
    # Disable authentication for tests
    app.dependency_overrides[check_token] = lambda: None
    return app


@pytest.fixture
def client(test_app):
    return TestClient(test_app)
