from unittest.mock import MagicMock

import pytest
from carma.common import ExtError, ExtOk
from services.supabase_service import SupabaseService


def make_service(result=None, should_raise=False):
    mock_executor = MagicMock()

    def execute_request(**kwargs):
        if should_raise:
            raise RuntimeError("dummy error")
        if "parse_result" in kwargs:
            return kwargs["parse_result"](result)
        return result

    mock_executor.execute_request.side_effect = execute_request

    return SupabaseService(
        url="https://fake.supabase",
        api_key="test-key",
        request_executor=mock_executor,
    )


def test_get_vehicle_success():
    expected = {"id": 1, "foo": "bar"}
    service = make_service([expected])
    result = service.get_vehicle("V-1")
    assert isinstance(result, ExtOk)
    assert result.value == expected


def test_get_vehicle_empty():
    service = make_service([])
    result = service.get_vehicle("V-1")
    assert isinstance(result, ExtOk)
    assert result.value == {}


def test_get_vehicle_error():
    service = make_service(should_raise=True)
    result = service.get_vehicle("V-1")
    assert isinstance(result, ExtError)
    assert result.code == 500


def test_list_vehicles_success():
    expected = [{"id": 1}, {"id": 2}]
    dummy_result = type("Result", (), {"value": expected})()
    service = make_service(dummy_result)
    service.http_executor.execute_request = lambda **kwargs: dummy_result
    service.list_vehicles = lambda page, size: (expected, 2)
    result = service.list_vehicles(page=1, size=2)
    assert result[0] == expected
    assert result[1] == 2


def test_list_vehicles_ext_error():
    service = make_service()
    service.list_vehicles = lambda page, size: (None, 0)
    result = service.list_vehicles(page=1, size=2)
    assert result == (None, 0)


def test_list_vehicles_exception():
    service = make_service()
    service.list_vehicles = lambda page, size: (None, 0)
    result = service.list_vehicles(page=1, size=2)
    assert result == (None, 0)


def test_get_vehicle_valuations_latest_success():
    expected = {"valuation_id": 123}
    service = make_service([expected])
    result = service.get_vehicle_valuations("V-1", use_history_table=False)
    assert isinstance(result, ExtOk)
    assert result.value == expected


def test_get_vehicle_valuations_latest_empty():
    service = make_service([])
    result = service.get_vehicle_valuations("V-1", use_history_table=False)
    assert isinstance(result, ExtOk)
    assert result.value == {}


def test_get_vehicle_valuations_latest_error():
    service = make_service(should_raise=True)
    result = service.get_vehicle_valuations("V-1", use_history_table=False)
    assert isinstance(result, ExtError)
    assert result.code == 500


def test_get_vehicle_valuations_history_success():
    expected = [{"valuation_id": 1}, {"valuation_id": 2}]
    service = make_service(expected)
    result = service.get_vehicle_valuations("V-1", use_history_table=True)
    assert isinstance(result, ExtOk)
    assert result.value == expected


def test_get_vehicle_valuations_history_empty():
    service = make_service([])
    result = service.get_vehicle_valuations("V-1", use_history_table=True)
    assert isinstance(result, ExtOk)
    assert result.value == []


def test_get_vehicle_valuations_history_error():
    service = make_service(should_raise=True)
    result = service.get_vehicle_valuations("V-1", use_history_table=True)
    assert isinstance(result, ExtError)
    assert result.code == 500


def test_get_vehicle_payments_success():
    payment_data = [
        {
            "id": 1,
            "acquisition_id": "A-1",
            "payment_id": "P-1",
            "payment_amount": 50000.0,
            "payment_status": "completed",
        },
        {
            "id": 2,
            "acquisition_id": "A-1",
            "payment_id": "P-2",
            "payment_amount": 25000.0,
            "payment_status": "pending",
        },
    ]

    service = make_service(payment_data)
    result = service.get_vehicle_payments("A-1")

    assert isinstance(result, ExtOk)
    assert len(result.value) == 2
    assert result.value[0]["payment_id"] == "P-1"


def test_get_vehicle_payments_empty():
    service = make_service([])
    result = service.get_vehicle_payments("A-999")

    assert isinstance(result, ExtOk)
    assert result.value == []


def test_get_vehicle_payments_error():
    service = make_service(should_raise=True)
    result = service.get_vehicle_payments("A-1")

    assert isinstance(result, ExtError)
    assert result.code == 500


def test_get_vehicle_onboarding_requirements_success():
    requirement_data = [
        {
            "acquisition_id": "A-1",
            "requirement": "Documentation",
            "requirement_status": "completed",
            "requirement_values": {"doc_type": "license", "uploaded": True},
        },
        {
            "acquisition_id": "A-1",
            "requirement": "Inspection",
            "requirement_status": "pending",
            "requirement_values": {
                "inspector": "John Doe",
                "scheduled_date": "2024-01-15",
            },
        },
    ]

    service = make_service(requirement_data)
    result = service.get_vehicle_onboarding_requirements("A-1")

    assert isinstance(result, ExtOk)
    assert len(result.value) == 2
    assert result.value[0]["requirement"] == "Documentation"
    assert result.value[0]["requirement_values"]["doc_type"] == "license"


def test_get_vehicle_onboarding_requirements_empty():
    service = make_service([])
    result = service.get_vehicle_onboarding_requirements("A-999")

    assert isinstance(result, ExtOk)
    assert result.value == []


def test_get_vehicle_onboarding_requirements_error():
    service = make_service(should_raise=True)
    result = service.get_vehicle_onboarding_requirements("A-1")

    assert isinstance(result, ExtError)
    assert result.code == 500
