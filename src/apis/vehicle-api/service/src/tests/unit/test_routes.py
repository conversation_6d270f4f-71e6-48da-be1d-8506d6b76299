from unittest.mock import MagicMock, patch

import pytest

# Import the app creation functions
from app import create_app, create_container
from carma.common import ExtOk
from container import Container
from fastapi.testclient import TestClient
from models import Vehicle, VehicleWithBCStatus
from router.routes import router
from router.utils import check_token
from services.mapping_service import VehicleMappingService
from services.supabase_service import SupabaseService


@pytest.fixture
def api_version():
    return "v2"


@pytest.fixture
def app():
    """Create a FastAPI app with proper DI container setup."""
    container = create_container()
    app = create_app(container)
    # Disable authentication for tests
    app.dependency_overrides[check_token] = lambda: None
    return app


@pytest.fixture
def client(app):
    """Create a test client with the app."""
    return TestClient(app)


@pytest.fixture
def mock_supabase_service():
    """Create a mock SupabaseService."""
    return MagicMock(spec=SupabaseService)


@pytest.fixture
def mock_mapping_service():
    """Create a mock VehicleMappingService."""
    return MagicMock(spec=VehicleMappingService)


@pytest.fixture
def valid_vehicle_data():
    """Create valid vehicle test data."""
    return {
        "id": 123,
        "make": "Toyota",
        "model": "Camry",
        "year": 2020,
        "vin": "ABC123XYZ",
        "acquisition_id": "A-123",
        "acquisition_version": 1,
        "acquisition_version_created_by": "test_user",
        "acquisition_version_created_at": "2024-01-01T00:00:00Z",
        "acquisition_created_at": "2024-01-01T00:00:00Z",
        "rego": "ABC123",
        "state": "NSW",
        "keys": "2",
        "transmission": "manual",
        "odometer": 50000,
        "bc_required_field": "value",
    }


@pytest.fixture
def invalid_vehicle_data():
    """Create invalid vehicle test data."""
    return {
        "id": 124,
        "make": "Honda",
        "model": "Civic",
        "year": 2020,
        "vin": "DEF456UVW",
        "acquisition_id": "A-124",
        "acquisition_version": 1,
        "acquisition_version_created_by": "test_user",
        "acquisition_version_created_at": "2024-01-01T00:00:00Z",
        "acquisition_created_at": "2024-01-01T00:00:00Z",
        "rego": "",  # Empty string to make it invalid for BC
        "state": "NSW",
        "keys": "2",
        "transmission": "manual",
        "odometer": 50000,
        "bc_required_field": "value",
    }


def test_list_vehicles_without_validation(
    client,
    api_version,
    mock_supabase_service,
    mock_mapping_service,
    valid_vehicle_data,
    invalid_vehicle_data,
):
    """Test listing vehicles without BC validation."""
    # Setup mocks
    mock_supabase_service.list_vehicles.return_value = (
        [valid_vehicle_data, invalid_vehicle_data],
        2,
    )
    mock_mapping_service.map_vehicle.side_effect = [
        (Vehicle(**valid_vehicle_data), True),
        (Vehicle(**invalid_vehicle_data), False),
    ]

    # Override dependencies
    container = client.app.container
    container.supabase_service.override(mock_supabase_service)
    client.app.dependency_overrides[VehicleMappingService] = (
        lambda: mock_mapping_service
    )

    # Make request
    response = client.get(
        f"/api/{api_version}/vehicles?page=1&size=20",
        headers={"X-API-KEY": "test_token"},
    )

    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert len(data["data"]) == 2
    assert "ready_for_bc" not in data["data"][0]
    assert data["meta"]["validate_bc"] is False

    # Verify mocks were called correctly
    mock_supabase_service.list_vehicles.assert_called_once_with(page=1, size=20)
    assert mock_mapping_service.map_vehicle.call_count == 2


def test_list_vehicles_with_validation(
    client,
    api_version,
    mock_supabase_service,
    mock_mapping_service,
    valid_vehicle_data,
    invalid_vehicle_data,
):
    """Test listing vehicles with BC validation."""
    # Setup mocks
    mock_supabase_service.list_vehicles.return_value = (
        [valid_vehicle_data, invalid_vehicle_data],
        2,
    )
    mock_mapping_service.map_vehicle.side_effect = [
        (Vehicle(**valid_vehicle_data), True),
        (Vehicle(**invalid_vehicle_data), False),
    ]

    # Override dependencies
    container = client.app.container
    container.supabase_service.override(mock_supabase_service)
    client.app.dependency_overrides[VehicleMappingService] = (
        lambda: mock_mapping_service
    )

    # Make request
    response = client.get(
        f"/api/{api_version}/vehicles?page=1&size=20&validate_bc=true",
        headers={"X-API-KEY": "test_token"},
    )

    # Assert response
    assert response.status_code == 200
    data = response.json()
    assert len(data["data"]) == 2

    # Check first vehicle (valid)
    assert data["data"][0]["ready_for_bc"] is True
    assert data["data"][0]["id"] == valid_vehicle_data["id"]

    # Check second vehicle (invalid)
    assert data["data"][1]["ready_for_bc"] is False
    assert data["data"][1]["id"] == invalid_vehicle_data["id"]

    # Check metadata
    assert data["meta"]["validate_bc"] is True
    assert data["meta"]["page"] == 1
    assert data["meta"]["size"] == 20
    assert data["meta"]["total_records"] == 2

    # Verify mocks were called correctly
    mock_supabase_service.list_vehicles.assert_called_once_with(page=1, size=20)
    assert mock_mapping_service.map_vehicle.call_count == 2


def test_list_vehicles_no_results(
    client, api_version, mock_supabase_service, mock_mapping_service
):
    """Test listing vehicles when no results are found."""
    # Setup mocks
    mock_supabase_service.list_vehicles.return_value = (None, 0)

    # Override dependencies
    container = client.app.container
    container.supabase_service.override(mock_supabase_service)
    client.app.dependency_overrides[VehicleMappingService] = (
        lambda: mock_mapping_service
    )

    # Make request
    response = client.get(
        f"/api/{api_version}/vehicles?page=1&size=20",
        headers={"X-API-KEY": "test_token"},
    )

    # Assert response
    assert response.status_code == 404
    assert response.json()["detail"] == "No vehicles found"

    # Verify mocks were called correctly
    mock_supabase_service.list_vehicles.assert_called_once_with(page=1, size=20)
    mock_mapping_service.map_vehicle.assert_not_called()


def test_list_vehicles_invalid_pagination(client, api_version):
    """Test listing vehicles with invalid pagination parameters."""
    # Test invalid page
    response = client.get(
        f"/api/{api_version}/vehicles?page=0&size=20",
        headers={"X-API-KEY": "test_token"},
    )
    assert response.status_code == 422

    # Test invalid size
    response = client.get(
        f"/api/{api_version}/vehicles?page=1&size=0",
        headers={"X-API-KEY": "test_token"},
    )
    assert response.status_code == 422

    # Test size too large
    response = client.get(
        f"/api/{api_version}/vehicles?page=1&size=101",
        headers={"X-API-KEY": "test_token"},
    )
    assert response.status_code == 422
