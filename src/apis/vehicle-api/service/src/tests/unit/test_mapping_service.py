import pytest
from models import Vehicle
from pydantic import ValidationError
from services.mapping_service import VehicleMappingService


@pytest.fixture
def mapping_service():
    return VehicleMappingService()


@pytest.fixture
def valid_vehicle_data():
    return {
        "id": 123,
        "make": "Toyota",
        "model": "Camry",
        "year": 2020,
        "vin": "ABC123XYZ",
        "acquisition_id": "A-123",
        "acquisition_version": 1,
        "acquisition_version_created_by": "test_user",
        "acquisition_version_created_at": "2024-01-01T00:00:00Z",
        "acquisition_created_at": "2024-01-01T00:00:00Z",
        "rego": "ABC123",
        "state": "NSW",
        "keys": "2",
        "transmission": "manual",
        "odometer": 50000,
        "bc_required_field": "value",  # Example of a BC required field
    }


@pytest.fixture
def invalid_vehicle_data():
    return {
        "id": 123,
        "make": "Toyota",
        "acquisition_id": "A-123",
        "acquisition_version": 1,
        "acquisition_version_created_by": "test_user",
        "acquisition_version_created_at": "2024-01-01T00:00:00Z",
        "acquisition_created_at": "2024-01-01T00:00:00Z",
        # Missing required fields: model, year, vin, rego, state, keys, transmission, odometer
        "bc_required_field": "value",
    }


def test_map_vehicle_valid_data(mapping_service, valid_vehicle_data):
    """Test mapping valid vehicle data."""
    vehicle, is_valid = mapping_service.map_vehicle(valid_vehicle_data)

    assert isinstance(vehicle, Vehicle)
    assert vehicle.id == valid_vehicle_data["id"]
    assert vehicle.make == valid_vehicle_data["make"]
    assert vehicle.model == valid_vehicle_data["model"]
    assert is_valid is True


def test_map_vehicle_invalid_data(mapping_service, invalid_vehicle_data):
    """Test mapping invalid vehicle data."""
    vehicle, is_valid = mapping_service.map_vehicle(invalid_vehicle_data)

    assert isinstance(vehicle, Vehicle)
    assert vehicle.id == invalid_vehicle_data["id"]
    assert vehicle.make == invalid_vehicle_data["make"]
    assert is_valid is False


def test_validate_vehicle_for_bc_valid(mapping_service, valid_vehicle_data):
    """Test BC validation for valid vehicle."""
    vehicle = Vehicle(**valid_vehicle_data)
    is_valid = mapping_service.validate_vehicle_for_bc(vehicle)
    assert is_valid is True


def test_validate_vehicle_for_bc_invalid(mapping_service, invalid_vehicle_data):
    """Test BC validation for invalid vehicle."""
    vehicle = Vehicle.model_construct(**invalid_vehicle_data)
    is_valid = mapping_service.validate_vehicle_for_bc(vehicle)
    assert is_valid is False


def test_validate_bc_required_fields_empty_string(mapping_service):
    """Test BC validation with empty string in required field."""
    data = {
        "id": "123",
        "make": "Toyota",
        "model": "Camry",
        "bc_required_field": "",  # Empty string should fail validation
    }
    vehicle = Vehicle.model_construct(**data)
    is_valid = mapping_service.validate_vehicle_for_bc(vehicle)
    assert is_valid is False


def test_validate_bc_required_fields_none(mapping_service):
    """Test BC validation with None in required field."""
    data = {
        "id": "123",
        "make": "Toyota",
        "model": "Camry",
        "bc_required_field": None,  # None should fail validation
    }
    vehicle = Vehicle.model_construct(**data)
    is_valid = mapping_service.validate_vehicle_for_bc(vehicle)
    assert is_valid is False
