import uvicorn
from carma.api_middleware.middleware import (
    add_correlation_id_log,
    add_trace_header_to_logs,
)
from carma.logs.intercept_handler import init_logging
from container import Container
from fastapi import Depends, FastAPI
from loguru import logger
from router import routes
from router import utils as router_utils
from settings import Settings
from slowapi import _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded

settings = Settings()

init_logging(
    service_name="vehicle-api",
    local=settings.local,
    level=settings.log_level,
)


def create_container() -> Container:
    container = Container()
    container.config.from_dict(settings.model_dump())

    return container

def create_app(container) -> FastAPI:
    app = FastAPI()
    app.state.limiter = routes.limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

    app.container = container

    container.wire(modules=[router_utils, routes])

    app.middleware("http")(add_correlation_id_log)
    app.middleware("http")(add_trace_header_to_logs)

    app.include_router(
        routes.router,
        prefix=f"/api/{settings.api_version}",
    )

    logger.debug("app container initialised")
    return app


app = create_app(create_container())


if __name__ == "__main__":
    uvicorn.run("app:app", host="0.0.0.0", port=5000, reload=True)
