from carma.http_utils import RequestExecutor
from dependency_injector import containers, providers
from services.supabase_service import SupabaseService


class Container(containers.DeclarativeContainer):
    config = providers.Configuration()

    request_executor = providers.Singleton(RequestExecutor)

    supabase_service = providers.Factory(
        SupabaseService,
        url=config.supabase_url,
        api_key=config.supabase_api_key,
        request_executor=request_executor,
    )
