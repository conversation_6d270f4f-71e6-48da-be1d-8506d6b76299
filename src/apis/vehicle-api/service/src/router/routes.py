from typing import List, Union

from container import Container
from dependency_injector.wiring import Provide, inject
from fastapi import (
    APIRouter,
    Body,
    Depends,
    Header,
    HTTPException,
    Query,
    Request,
    status,
)
from models import (
    Envelope,
    MetaData,
    OnboardingRequirement,
    Vehicle,
    VehiclePayment,
    VehicleWithBCStatus,
    Onboarding,
    Inspection
)
from router.utils import check_token
from services.mapping_service import VehicleMappingService
from services.supabase_service import SupabaseService
from slowapi import Limiter
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)
router = APIRouter()


@router.get("/healthz")
@router.get("/ready")
def ready_check():
    return {}


@router.get(
    "/vehicles",
    response_model=Envelope[list[Union[Vehicle, VehicleWithBCStatus]]],
    dependencies=[Depends(check_token)],
)
@inject
def list_vehicles(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    validate_bc: bool = Query(
        False, description="Validate vehicles for Business Central readiness"
    ),
    supabase: SupabaseService = Depends(Provide[Container.supabase_service]),
    mapper: VehicleMappingService = Depends(),
):
    """List vehicles with optional BC validation.

    Args:
        page: Page number for pagination
        size: Number of items per page
        validate_bc: Whether to validate vehicles for BC readiness
        supabase: Supabase service instance
        mapper: Vehicle mapping service instance

    Returns:
        Envelope containing list of vehicles with BC validation status if requested
    """
    raw = supabase.list_vehicles(page=page, size=size)
    if raw is None or (isinstance(raw, tuple) and raw[0] is None):
        raise HTTPException(404, "No vehicles found")

    # Handle ExtResult object
    if hasattr(raw, "value"):
        values = raw.value
        total_count = len(values) if values else 0
    else:
        # Handle tuple return for error cases
        values, total_count = raw
    mapped = [mapper.map_vehicle(r) for r in values]

    if validate_bc:
        # Return vehicles with BC validation status
        vehicles = [
            VehicleWithBCStatus(**vehicle.model_dump(), ready_for_bc=is_valid)
            for vehicle, is_valid in mapped
        ]
    else:
        # Return just the vehicles without BC validation
        vehicles = [vehicle for vehicle, _ in mapped]

    meta = MetaData(
        page=page, size=size, total_records=total_count, validate_bc=validate_bc
    )
    return Envelope(data=vehicles, meta=meta)


@router.get(
    "/vehicles/{id}",
    response_model=Envelope[Vehicle],
    dependencies=[Depends(check_token)],
)
@inject
def get_vehicle(
    id: str,
    supabase_service: SupabaseService = Depends(Provide[Container.supabase_service]),
    mapping_service: VehicleMappingService = Depends(),
):
    """Fetch a single vehicle by ID from Supabase and map to Pydantic model."""
    result = supabase_service.get_vehicle(id)
    if result.is_error():
        raise HTTPException(status_code=result.code, detail=result.message)
    if result.value is None or len(result.value) == 0:
        raise HTTPException(status_code=404, detail="Vehicle not found")
    model, ready_for_bc = mapping_service.map_vehicle(result.value)
    meta = MetaData(ready_for_bc=ready_for_bc)
    return Envelope(data=model, meta=meta)


@router.get(
    "/vehicles/{id}/valuations",
    response_model=Envelope[Union[dict, list[dict]]],
    dependencies=[Depends(check_token)],
)
@inject
def get_vehicle_valuations(
    id: str,
    supabase_service: SupabaseService = Depends(Provide[Container.supabase_service]),
    accept: str = Header(default="application/json"),
):
    """Fetch vehicle valuations for the given acquisition ID."""
    use_history = accept == "application/json+history"
    result = supabase_service.get_vehicle_valuations(id, use_history_table=use_history)
    if result.is_error():
        raise HTTPException(status_code=result.code, detail=result.message)
    if not result.value:
        raise HTTPException(status_code=404, detail="No valuations found")
    return Envelope(data=result.value, meta=MetaData(is_valid=True))


@router.put(
    "/vehicles/{id}",
    response_model=Envelope[Vehicle],
    dependencies=[Depends(check_token)],
    status_code=status.HTTP_200_OK,
)
@inject
def update_vehicle(
    id: str,
    update_fields: dict = Body(...),
    supabase_service: SupabaseService = Depends(Provide[Container.supabase_service]),
    mapping_service: VehicleMappingService = Depends(),
):
    """Update a vehicle by ID in Supabase."""
    result = supabase_service.update_vehicle(id, update_fields)
    if result.is_error():
        raise HTTPException(status_code=result.code, detail=result.message)
    if not result.value:
        raise HTTPException(status_code=404, detail="Vehicle not found or not updated")
    model, ready_for_bc = mapping_service.map_vehicle(result.value)
    meta = MetaData(ready_for_bc=ready_for_bc)
    return Envelope(data=model, meta=meta)


@router.patch(
    "/vehicles/{id}",
    response_model=Envelope[Vehicle],
    dependencies=[Depends(check_token)],
    status_code=status.HTTP_200_OK,
)
@inject
def patch_vehicle(
    id: str,
    update_fields: dict = Body(...),
    supabase_service: SupabaseService = Depends(Provide[Container.supabase_service]),
    mapping_service: VehicleMappingService = Depends(),
):
    """Patch a vehicle by ID in Supabase, allowing any field except 'id'."""
    result = supabase_service.patch_vehicle(id, update_fields)
    if result.is_error():
        raise HTTPException(status_code=result.code, detail=result.message)
    if not result.value:
        raise HTTPException(status_code=404, detail="Vehicle not found or not updated")
    model, ready_for_bc = mapping_service.map_vehicle(result.value)
    meta = MetaData(ready_for_bc=ready_for_bc)
    return Envelope(data=model, meta=meta)

@router.patch(
    "/onboarding/{id}/{version_id}",
    response_model=Onboarding,
    dependencies=[Depends(check_token)],
    status_code=status.HTTP_200_OK,
)
@inject
def patch_onboarding(
    id: str,
    version_id: str,
    update_fields: dict = Body(...),
    supabase_service: SupabaseService = Depends(Provide[Container.supabase_service])
):
    """Patch a vehicle by ID in Supabase, allowing any field except 'id'."""
    result = supabase_service.patch_onboarding(id, version_id, update_fields)
    if result.is_error():
        raise HTTPException(status_code=result.code, detail=result.message)
    if not result.value:
        raise HTTPException(status_code=404, detail="Onboarding not found or not updated")

    return Onboarding(**result.value[0])


@router.get(
    "/vehicles/{id}/payments",
    response_model=Envelope[list[VehiclePayment]],
    dependencies=[Depends(check_token)],
)
@inject
def get_vehicle_payments(
    id: str,
    supabase_service: SupabaseService = Depends(Provide[Container.supabase_service]),
):
    """Fetch vehicle payments for the given acquisition ID."""
    result = supabase_service.get_vehicle_payments(id)
    if result.is_error():
        raise HTTPException(status_code=result.code, detail=result.message)
    if not result.value:
        return Envelope(data=[], meta=MetaData())

    # Convert raw data to VehiclePayment models
    payments = [VehiclePayment(**payment) for payment in result.value]
    return Envelope(data=payments, meta=MetaData())


@router.get(
    "/vehicles/{id}/onboarding_requirements",
    response_model=Envelope[list[OnboardingRequirement]],
    dependencies=[Depends(check_token)],
)
@inject
def get_vehicle_onboarding_requirements(
    id: str,
    supabase_service: SupabaseService = Depends(Provide[Container.supabase_service]),
):
    """Fetch vehicle onboarding requirements for the given acquisition ID."""
    result = supabase_service.get_vehicle_onboarding_requirements(id)
    if result.is_error():
        raise HTTPException(status_code=result.code, detail=result.message)
    if not result.value:
        return Envelope(data=[], meta=MetaData())

    # Convert raw data to OnboardingRequirement models
    requirements = [
        OnboardingRequirement(**requirement) for requirement in result.value
    ]
    return Envelope(data=requirements, meta=MetaData())

@router.get(
    "/vehicles/{id}/onboarding",
    response_model=Envelope[Onboarding],
    dependencies=[Depends(check_token)],
)
@inject
def get_vehicle_onboarding(
    id: str,
    supabase_service: SupabaseService = Depends(Provide[Container.supabase_service]),
):
    """Fetch vehicle onboarding requirements for the given acquisition ID."""
    result = supabase_service.get_vehicle_onboarding(id)
    if result.is_error():
        raise HTTPException(status_code=result.code, detail=result.message)
    if not result.value:
        raise HTTPException(status_code=404, detail="Vehicle onboarding not found or not updated")

    if isinstance(result.value, list) and len(result.value) == 0:
        raise HTTPException(status_code=404, detail="Vehicle onboarding not found")
    
    # Convert raw data to OnboardingRequirement models
    onboarding = Onboarding(**result.value[0])

    return Envelope(data=onboarding, meta=MetaData(is_valid=True))


@router.get(
    "/vehicles/{id}/inspection",
    response_model=Envelope[Inspection],
    dependencies=[Depends(check_token)],
)
@inject
def get_vehicle_inspection(
    id: str,
    supabase_service: SupabaseService = Depends(Provide[Container.supabase_service]),
):
    """Fetch vehicle onboarding requirements for the given acquisition ID."""
    result = supabase_service.get_latest_vehicle_inspection(id)
    if result.is_error():
        raise HTTPException(status_code=result.code, detail=result.message)
    if not result.value:
        raise HTTPException(status_code=404, detail="Vehicle inspection not found")

    if isinstance(result.value, list) and len(result.value) == 0:
        raise HTTPException(status_code=404, detail="Vehicle inspection not found")

    # Convert raw data to OnboardingRequirement models
    inspection = Inspection(**result.value[0])

    return Envelope(data=inspection, meta=MetaData(is_valid=True))
