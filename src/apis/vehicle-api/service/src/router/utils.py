from container import Container
from dependency_injector.wiring import Provide, inject
from fastapi import Depends, <PERSON><PERSON>, HTTPException


@inject
async def check_token(
    token: str | None = Header(None, alias="X-API-KEY"),
    expected_token: str = Depends(Provide[Container.config.expected_token]),
):
    if not token:
        raise HTTPException(401, "no auth header provided")

    if expected_token != token:
        raise HTTPException(401, f"invalid token provided: {token}")
