from typing import Optional

from models import Vehicle, VehicleValuation
from pydantic import ValidationError


class VehicleMappingService:
    """Service to map raw vehicle dict to Vehicle Pydantic model."""

    def _validate_bc_required_fields(self, model: Vehicle) -> bool:
        """Check if all bc_required fields are present and not empty.

        Args:
            model: The Vehicle model to validate

        Returns:
            bool: True if all bc_required fields are valid, False otherwise
        """
        for name, field in Vehicle.model_fields.items():
            if "bc_required" in (field.json_schema_extra or {}):
                value = getattr(model, name, None)
                if value is None or (isinstance(value, str) and not value):
                    return False
        return True

    def map_vehicle(self, data: dict) -> tuple[Vehicle, bool]:
        """Try to validate incoming data to Vehicle; if missing fields, construct without validation and mark invalid.
        Also check that all bc_required fields are present and not empty.

        Args:
            data: Raw vehicle data dictionary

        Returns:
            tuple[Vehicle, bool]: The mapped vehicle model and whether it's valid for BC
        """
        try:
            model = Vehicle(**data)
        except ValidationError:
            model = Vehicle.model_construct(**data)
            # If model can't be constructed, it's invalid
            return model, False

        return model, self._validate_bc_required_fields(model)

    def validate_vehicle_for_bc(self, vehicle: Vehicle) -> bool:
        """Validate if a vehicle is ready for Business Central.

        Args:
            vehicle: The Vehicle model to validate

        Returns:
            bool: True if the vehicle is ready for BC, False otherwise
        """
        return self._validate_bc_required_fields(vehicle)
