from carma.common import ExtError, ExtOk, ExtResult
from carma.http_utils import RequestExecutor
from loguru import logger


class SupabaseService:
    def __init__(
        self,
        url: str,
        api_key: str,
        request_executor: RequestExecutor,
    ):
        self.url = url
        self.api_key = api_key
        self.http_executor = request_executor
        self.headers = {"apikey": self.api_key, "Prefer": "return=representation"}

    def get_vehicle(self, vehicle_id: str) -> ExtResult[dict]:
        """Fetch a single vehicle by ID from Supabase."""
        url = f"{self.url}/rest/v1/acquisition_list_view?acquisition_id=eq.{vehicle_id}"

        try:
            return self.http_executor.execute_request(
                verb="GET",
                url=url,
                headers=self.headers,
                parse_result=lambda data: ExtOk(data[0]) if data else ExtOk({}),
                parse_error=lambda e: ExtError(
                    code=500, message=f"Error fetching vehicle {e}"
                ),
            )
        except Exception as e:
            logger.error(f"Error fetching vehicle {vehicle_id}: {e}", exc_info=True)
            return ExtError(code=500, message="Error fetching vehicle")

    def list_vehicles(self, page: int, size: int) -> tuple[list[dict] | None, int]:
        """Fetch a paginated list of vehicles and return (records, total_count)."""
        offset = (page - 1) * size
        headers = self.headers.copy()
        headers["Range-Unit"] = "items"
        headers["Range"] = f"{offset}-{offset + size - 1}"
        headers["Prefer"] = "count=exact"
        url = f"{self.url}/rest/v1/acquisition_list_view"
        result = self.http_executor.execute_request(
            verb="GET",
            url=url,
            headers=headers,
            parse_results_with_headers=True,
            parse_result=lambda data, resp_headers: ExtOk(data),
            parse_error=lambda e: ExtError(
                code=500, message=f"Error listing vehicles {e}"
            ),
        )
        if isinstance(result, ExtError):
            logger.error(
                f"SupabaseService.list_vehicles error: {result.code} {result.message}"
            )
            return None, 0
        return result

    def get_vehicle_valuations(
        self, vehicle_id: str, use_history_table: bool = False
    ) -> ExtResult[list | dict]:
        """Fetch vehicle valuations for a vehicle. If use_history_table is True, fetch all history records from the valuations table, otherwise fetch the latest from the view."""
        try:
            if use_history_table:
                url = f"{self.url}/rest/v1/valuations?acquisition_id=eq.{vehicle_id}"
                return self.http_executor.execute_request(
                    verb="GET",
                    url=url,
                    headers=self.headers,
                    parse_result=lambda data: ExtOk(data) if data else ExtOk([]),
                )
            else:
                url = f"{self.url}/rest/v1/valuation_list_view?acquisition_id=eq.{vehicle_id}"
                return self.http_executor.execute_request(
                    verb="GET",
                    url=url,
                    headers=self.headers,
                    parse_result=lambda data: ExtOk(data[0]) if data else ExtOk({}),
                )
        except Exception as e:
            logger.error(
                f"Error fetching vehicle valuations for vehicle_id {vehicle_id}: {e}",
                exc_info=True,
            )
            return ExtError(code=500, message="Error fetching vehicle valuations")

    def update_vehicle(self, vehicle_id: str, update_fields: dict) -> ExtResult[dict]:
        """Update a vehicle in Supabase by ID using PUT."""
        url = f"{self.url}/rest/v1/acquisition_list_view?acquisition_id=eq.{vehicle_id}"
        try:
            return self.http_executor.execute_request(
                verb="PUT",
                url=url,
                headers=self.headers,
                json=update_fields,
                parse_result=lambda data: ExtOk(data[0]) if data else ExtOk({}),
                parse_error=lambda e: ExtError(
                    code=500, message=f"Error updating vehicle {e}"
                ),
            )
        except Exception as e:
            logger.error(f"Error updating vehicle {vehicle_id}: {e}", exc_info=True)
            return ExtError(code=500, message="Error updating vehicle")

    def patch_vehicle(self, vehicle_id: str, update_fields: dict) -> ExtResult[dict]:
        """Patch a vehicle in Supabase by ID, allowing any field except 'id'."""
        update_fields = {k: v for k, v in update_fields.items()}
        url = f"{self.url}/rest/v1/acquisition?acquisition_id=eq.{vehicle_id}"
        try:
            return self.http_executor.execute_request(
                verb="PATCH",
                url=url,
                headers=self.headers,
                json=update_fields,
                parse_result=lambda data: ExtOk(data[0]) if data else ExtOk({}),
                parse_error=lambda e: ExtError(
                    code=500, message=f"Error patching vehicle {e}"
                ),
            )
        except Exception as e:
            logger.error(f"Error patching vehicle {vehicle_id}: {e}", exc_info=True)
            return ExtError(code=500, message="Error patching vehicle")
        
    def patch_onboarding(self, onboarding_id: str, onboarding_version: int, update_fields: dict) -> ExtResult[dict]:
        """Patch a vehicle in Supabase by ID, allowing any field except 'id'."""
        update_fields = {k: v for k, v in update_fields.items()}
        url = f"{self.url}/rest/v1/onboarding?onboarding_id=eq.{onboarding_id}&onboarding_version=eq.{onboarding_version}"
        
        def parse_response(data: list) -> ExtOk| ExtError:
            if not data:
                return ExtError(code=500, message=f"Unable to update onboarding with id {onboarding_id}, version {onboarding_version}")
            
            return ExtOk(data)
        
        def parse_error(response) -> ExtError:
            return ExtError(
                code=response.status_code,
                message=f"status_code={response.status_code}, message={response.text}",
            )
            
        return self.http_executor.execute_request(
            verb="PATCH",
            url=url,
            headers=self.headers,
            payload=update_fields,
            parse_result=parse_response,
            parse_error=parse_error,
        )

    def get_vehicle_payments(self, acquisition_id: str) -> ExtResult[list[dict]]:
        """Fetch vehicle payments by acquisition ID from Supabase payment_list_view."""
        url = f"{self.url}/rest/v1/payment_list_view?acquisition_id=eq.{acquisition_id}"
        try:
            return self.http_executor.execute_request(
                verb="GET",
                url=url,
                headers=self.headers,
                parse_result=lambda data: ExtOk(data) if data else ExtOk([]),
                parse_error=lambda e: ExtError(
                    code=500, message=f"Error fetching vehicle payments {e}"
                ),
            )
        except Exception as e:
            logger.error(
                f"Error fetching vehicle payments for {acquisition_id}: {e}",
                exc_info=True,
            )
            return ExtError(code=500, message="Error fetching vehicle payments")

    def get_vehicle_onboarding_requirements(
        self, acquisition_id: str
    ) -> ExtResult[list[dict]]:
        """Fetch vehicle onboarding requirements by acquisition ID from Supabase onboarding_requirements table."""
        url = f"{self.url}/rest/v1/onboarding_requirements?acquisition_id=eq.{acquisition_id}"
        try:
            return self.http_executor.execute_request(
                verb="GET",
                url=url,
                headers=self.headers,
                parse_result=lambda data: ExtOk(data) if data else ExtOk([]),
                parse_error=lambda e: ExtError(
                    code=500,
                    message=f"Error fetching vehicle onboarding requirements {e}",
                ),
            )
        except Exception as e:
            logger.error(
                f"Error fetching vehicle onboarding requirements for {acquisition_id}: {e}",
                exc_info=True,
            )
            return ExtError(
                code=500, message="Error fetching vehicle onboarding requirements"
            )

    def get_vehicle_onboarding(
        self, acquisition_id: str
    ) -> ExtResult[list[dict]]:
        """Fetch vehicle onboarding requirements by acquisition ID from Supabase onboarding_requirements table."""
        url = f"{self.url}/rest/v1/onboarding?acquisition_id=eq.{acquisition_id}&order=onboarding_version.desc,onboarding_version_created_at.desc&limit=1"
        try:
            return self.http_executor.execute_request(
                verb="GET",
                url=url,
                headers=self.headers,
                parse_result=lambda data: ExtOk(data) if data else ExtOk([]),
                parse_error=lambda e: ExtError(
                    code=500,
                    message=f"Error fetching vehicle onboarding requirements {e}",
                ),
            )
        except Exception as e:
            logger.error(
                f"Error fetching vehicle onboarding requirements for {acquisition_id}: {e}",
                exc_info=True,
            )
            return ExtError(
                code=500, message="Error fetching vehicle onboarding requirements"
            )

    def get_latest_vehicle_inspection(
        self, acquisition_id: str
    ) -> ExtResult[list[dict]]:
        """Fetch vehicle inspection by acquisition ID from Supabase inspection table."""
        url = f"{self.url}/rest/v1/inspections?acquisition_id=eq.{acquisition_id}&order=inspection_version.desc,inspection_version_created_at.desc&limit=1"
        try:
            return self.http_executor.execute_request(
                verb="GET",
                url=url,
                headers=self.headers,
                parse_result=lambda data: ExtOk(data) if data else ExtOk([]),
                parse_error=lambda e: ExtError(
                    code=500,
                    message=f"Error fetching vehicle onboarding requirements {e}",
                ),
            )
        except Exception as e:
            logger.error(
                f"Error fetching vehicle onboarding requirements for {acquisition_id}: {e}",
                exc_info=True,
            )
            return ExtError(
                code=500, message="Error fetching vehicle onboarding requirements"
            )