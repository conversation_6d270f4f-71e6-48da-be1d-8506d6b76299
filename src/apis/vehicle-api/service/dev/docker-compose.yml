version: "3.10"

services:
  vehicle-api:
    image: vehicle-api
    container_name: vehicle-api
    build:
      context: ../..
      dockerfile: Dockerfile
      target: dev
    working_dir: /app
    ports:
      - 5678:5678
      - 5000:5000
    env_file:
      - local.env
    volumes:
      - ../src:/app
    entrypoint:
      [
        "python3",
        "-m",
        "debugpy",
        "--listen",
        "0.0.0.0:5678",
        "-m",
        "app",
        "--wait-for-client",
      ]
