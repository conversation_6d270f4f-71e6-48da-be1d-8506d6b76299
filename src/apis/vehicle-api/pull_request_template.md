
## Definition of done

- [ ] My code is unit tested
- [ ] The code coverage is 80% or greater
- [ ] My code uses Python typing where possible
- [ ] My code is linted using Black
- [ ] My code uses Poetry to manage the packages
- [ ] My code uses FastAPI as the web framework where applicable / possible
- [ ] My code uses Carma Common where applicable
- [ ] I've rebased / squashed all commits and my commit message is short, concise and includes ticket number
- [ ] The service has readiness and health endpoints configured - where appropriate
