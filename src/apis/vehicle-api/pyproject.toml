[tool.poetry]
name = "vehicle-api"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.10,<3.11"
carma = {version = "^3.5.3" }
dependency-injector = "^4.48.1"
expression = "4.3.0"
fastapi = "^0.111.0"
slowapi = "^0.1.9"
boto3 = "^1.34.146"
pydantic = "^2.10.2"
python-multipart = "^0.0.20"


[tool.poetry.group.dev.dependencies]
debugpy = "^1.8.1"
pytest = "^8.2.2"
pytest-cov = "^5.0.0"
pytest-mock = "^3.14.0"
requests-mock = "^1.12.1"

[tool.isort]
profile = "black"

[tool.pytest.ini_options]
pythonpath = [
    ".",
    "service/src",
    "service/src/tests/unit",
    "service/src/tests/e2e",
]
filterwarnings = [
    "ignore::DeprecationWarning:pkg_resources.*:",
    "ignore::DeprecationWarning:google.rpc.*:",
]

[tool.coverage.xml]
output = "service/src/coverage.xml"

[[tool.poetry.source]]
name = "google"
url = "https://australia-southeast1-python.pkg.dev/carma-dev-tooling/carma-python/simple/"
priority = "supplemental"


[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
