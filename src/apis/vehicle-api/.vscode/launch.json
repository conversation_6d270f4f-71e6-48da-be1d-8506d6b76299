{"version": "0.2.0", "configurations": [{"name": "Debug Pytest - Unit Tests", "type": "python", "request": "launch", "module": "pytest", "args": ["service/src/tests/unit", "-v"], "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}/service/src"}}, {"name": "Python: Debug FastAPI", "type": "python", "request": "launch", "module": "u<PERSON><PERSON>", "cwd": "${workspaceFolder}/service/src", "envFile": "${workspaceFolder}/service/dev/dev.env", "args": ["app:app", "--host", "0.0.0.0", "--port", "5000", "--reload"], "jinja": true, "justMyCode": true}]}