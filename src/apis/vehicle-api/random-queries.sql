SELECT
  id,
  onboarding_id,
  acquisition_id,
  acquisition_channel,
  distribution_channel,
  onboarding_status,
  onboarding_created_at,
  onboarding_completed_at,
  onboarding_completed_by,
  purchase_date,
  purchase_price,
  vehicle_priority,
  sku,
  rego,
  acquisition_representative,
  representative,
  hubspot_owner_id,
  hubspot_deal_id,
  hubspot_pipeline_name,
  hubspot_stage_name,
  build_year,
  make,
  model,
  series,
  badge,
  transmission,
  fuel_type,
  vehicle_short_description_1,
  vehicle_short_description_2,
  customer_first_name,
  customer_last_name,
  valuation_status,
  valuation_completed_at,
  inspection_id,
  inspection_status,
  inspection_created_at,
  inspection_completed_at,
  inspection_completed_by,
  inspector,
  requirement_status,
  collected_at,
  onboarding_notes,
  onboarding_calculated_state,
  payment_calculated_state
  
FROM 
  onboarding_list_view

ORDER BY 
  onboarding_created_at DESC;



SELECT * from acquisitions a
join onboarding b on a.acquisition_id = b.acquisition_id