FROM australia-southeast1-docker.pkg.dev/carma-dev-tooling/carma-docker/carma-python:3.10.0 AS python-base

# Used to build dependencies
FROM python-base AS builder-base
# Install Poetry - respects $POETRY_VERSION & $POETRY_HOME
ENV POETRY_VERSION=1.6.1
RUN  curl -sSL https://install.python-poetry.org | python3 -

# We copy our Python requirements here to cache them
# and install only runtime deps using poetry
WORKDIR $PYSETUP_PATH
COPY ./poetry.lock ./pyproject.toml ./

# Keyring for gcp artifact registry authentication
RUN poetry self add "keyrings.google-artifactregistry-auth==1.1.2"

# Install non dev dependencies
RUN GOOGLE_APPLICATION_CREDENTIALS='/gac.json' poetry install --no-dev --no-root

# 'dev' stage installs all dev deps and can be used to develop code.
# For example using docker-compose to mount local volume under /app
FROM python-base AS dev

# Copying poetry and venv into image
COPY --from=builder-base $POETRY_HOME $POETRY_HOME
COPY --from=builder-base $PYSETUP_PATH $PYSETUP_PATH

# venv already has runtime deps installed we get a quicker install
WORKDIR $PYSETUP_PATH
RUN GOOGLE_APPLICATION_CREDENTIALS='/gac.json' poetry install --no-root

WORKDIR /app
COPY ./service/src .

CMD ["python", "app.py"]


FROM dev AS lint

RUN black --check . --config $PYSETUP_PATH/pyproject.toml
RUN isort . --recursive --check-only --settings-path $PYSETUP_PATH/pyproject.toml
CMD ["tail", "-f", "/dev/null"]


FROM dev AS tests

COPY ./service/src/tests/unit/ /app/tests/unit

ENTRYPOINT ["pytest", "./tests/unit/", "--cov"]

FROM dev AS e2e-tests

COPY ./service/src/tests/e2e/ /app/tests/e2e

ENTRYPOINT ["pytest", "./tests/e2e/"]


# 'production' stage uses the clean 'python-base' stage and copies
# in only our runtime deps that were installed in the 'builder-base'
FROM python-base AS production

COPY --from=builder-base $VENV_PATH $VENV_PATH

WORKDIR /app
COPY ./service/src .

CMD exec gunicorn  -k uvicorn.workers.UvicornWorker --bind :5000 --workers 1 --threads 8 --timeout 0 app:app
