@supabase_url={{$dotenv supabase_url}}
@supabase_api_key={{$dotenv supabase_api_key}}
@api_version={{$dotenv api_version}}
@expected_token={{$dotenv expected_token}}
@server_url=http://localhost:5000
# @server_url=https://carma-stg.com.au
# @server_url=https://carma-uat.com.au

###
@acquisition_id = B-134454
# @name supabase-get-acquisition-by-id

GET {{supabase_url}}/rest/v1/acquisition_list_view?acquisition_id=eq.{{acquisition_id}}
apiKey: {{supabase_api_key}}

@acquisition_id = B-136900
@acquisition_version_id = 78373
### @name supabase-get-vehicle-onboarding-by-acquisition-id
GET {{supabase_url}}/rest/v1/onboarding?acquisition_id=eq.{{acquisition_id}}&order=onboarding_version.desc,onboarding_created_at.desc
apikey: {{supabase_api_key}}
Prefer: return=representation

### @name supabase-get-vehicle-inspection-by-acquisition-id
GET {{supabase_url}}/rest/v1/inspections?acquisition_id=eq.{{acquisition_id}}&order=inspection_version.desc&inspection_version_created_at.desc&limit=1
apikey: {{supabase_api_key}}
Prefer: return=representation


#### @name supabase-get-vehicles

GET {{supabase_url}}/rest/v1/acquisition_list_view
Range-Unit: items
Prefer: count=exact
Range: 1-20
Prefer: return=representation
apiKey: {{supabase_api_key}}


@onboarding_id = 13bfba22-2a39-4c5a-86f0-d9ce15b4a7cd
@onboarding_version_id = 10
#### @name supabase-patch-onboarding
PATCH {{supabase_url}}/rest/v1/onboarding?onboarding_id=eq.{{onboarding_id}}&onboarding_version=eq.{{onboarding_version_id}}
apiKey: {{supabase_api_key}}
Content-Type: application/json
Prefer: return=representation

{
    "sku": "999968"
}


### @name vehicle-api-get-vehicles

GET {{server_url}}/api/v2/vehicles
Content-Type: application/json
X-API-KEY: {{expected_token}}
page: 1
size: 20


### @name vehicle-api-get-vehicles-by-acquistion-id
###
GET {{server_url}}/api/v2/vehicles/{{acquisition_id}}
Content-Type: application/json
X-API-KEY: {{expected_token}}

#@acquisition_id = B-112230
### @name vehicle-api-get-vehicle-valuation
###
GET {{server_url}}/api/v2/vehicles/{{acquisition_id}}/valuations
Content-Type: application/json
X-API-KEY: {{expected_token}}

#@acquisition_id = B-135169
### @name vehicle-api-get-vehicle-payments
###
GET {{server_url}}/api/v2/vehicles/{{acquisition_id}}/payments
Content-Type: application/json
X-API-KEY: {{expected_token}}


#@acquisition_id = B-132704
### @name vehicle-api-get-vehicle-onboarding
###
GET {{server_url}}/api/v2/vehicles/{{acquisition_id}}/onboarding
Content-Type: application/json
X-API-KEY: {{expected_token}}

### @name vehicle-api-get-vehicle-onboarding
###
GET {{server_url}}/api/v2/vehicles/{{acquisition_id}}/onboarding_requirements
Content-Type: application/json
X-API-KEY: {{expected_token}}


#@acquisition_id = B-112718
### @name vehicle-api-get-vehicle-inspection
###
GET {{server_url}}/api/v2/vehicles/{{acquisition_id}}/inspection
Content-Type: application/json
X-API-KEY: {{expected_token}}


@onboarding_id = 13bfba22-2a39-4c5a-86f0-d9ce15b4a7cd

#### @name vehicle-api-patch-onboarding
PATCH {{server_url}}/api/v2/onboarding/{{onboarding_id}}/{{onboarding_version_id}}
Content-Type: application/json
X-API-KEY: {{expected_token}}

{
    "sku": "777780"
}
